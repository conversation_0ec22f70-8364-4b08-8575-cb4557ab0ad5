@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 15, 23, 42;
  --background-rgb: 255, 255, 255;
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 248, 250, 252;
    --background-rgb: 15, 23, 42;
    --glass-bg: rgba(15, 23, 42, 0.25);
    --glass-border: rgba(255, 255, 255, 0.1);
  }
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  overflow-x: hidden;
}

/* Beautiful background patterns */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

@layer base {
  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent leading-tight;
  }
  h2 {
    @apply text-2xl md:text-3xl font-semibold mb-4 text-neutral-800 dark:text-neutral-200;
  }
  h3 {
    @apply text-xl md:text-2xl font-medium mb-3 text-neutral-700 dark:text-neutral-300;
  }
  h4 {
    @apply text-lg md:text-xl font-medium mb-2 text-neutral-700 dark:text-neutral-300;
  }
  a {
    @apply text-primary-600 hover:text-primary-700 transition-all duration-300 hover:underline decoration-2 underline-offset-4;
  }
  p {
    @apply mb-4 text-neutral-600 dark:text-neutral-400 leading-relaxed;
  }
}

@layer components {
  /* Button Styles */
  .btn {
    @apply relative inline-flex items-center justify-center px-6 py-3 rounded-2xl font-medium transition-all duration-300 transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg shadow-primary-500/25 hover:shadow-xl hover:shadow-primary-500/40 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600 text-white shadow-lg shadow-secondary-500/25 hover:shadow-xl hover:shadow-secondary-500/40 focus:ring-secondary-500;
  }

  .btn-accent {
    @apply bg-gradient-to-r from-accent-500 to-accent-600 text-white shadow-lg shadow-accent-500/25 hover:shadow-xl hover:shadow-accent-500/40 focus:ring-accent-500;
  }

  .btn-ghost {
    @apply bg-white/10 backdrop-blur-sm border border-white/20 text-neutral-700 dark:text-neutral-300 hover:bg-white/20 hover:border-white/30 focus:ring-white/50;
  }

  .btn-outline {
    @apply border-2 border-primary-500 text-primary-600 hover:bg-primary-500 hover:text-white focus:ring-primary-500;
  }

  /* Card Styles */
  .card {
    @apply relative rounded-3xl p-8 transition-all duration-500;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .card:hover {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .card-glass {
    @apply relative rounded-3xl p-8;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.1), 0 2px 10px -2px rgba(0, 0, 0, 0.04);
  }

  .card-gradient {
    @apply relative rounded-3xl p-8;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.1), 0 2px 10px -2px rgba(0, 0, 0, 0.04);
  }

  /* Input Styles */
  .input {
    @apply w-full px-4 py-3 rounded-2xl transition-all duration-300 focus:outline-none;
    background-color: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(229, 229, 229, 0.5);
    color: rgb(23, 23, 23);
  }

  .input:focus {
    border-color: rgba(99, 102, 241, 0.5);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.2);
  }

  .input:hover {
    border-color: rgba(212, 212, 212, 0.7);
  }

  .input-error {
    border-color: rgba(239, 68, 68, 0.5);
  }

  .input-error:focus {
    border-color: rgba(239, 68, 68, 0.5);
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.2);
  }

  /* Progress Bar */
  .progress-bar {
    @apply w-full h-3 bg-neutral-200 dark:bg-neutral-700 rounded-full overflow-hidden;
    background-color: rgba(229, 229, 229, 0.5);
  }

  .progress-fill {
    @apply h-full rounded-full transition-all duration-1000 ease-out relative;
    background: linear-gradient(to right, rgb(99, 102, 241), rgb(217, 70, 239));
  }

  .progress-fill::after {
    content: '';
    @apply absolute inset-0 rounded-full animate-pulse;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
  }

  /* Status Indicators */
  .status-pending {
    background: linear-gradient(to right, rgb(163, 163, 163), rgb(115, 115, 115));
  }

  .status-processing {
    background: linear-gradient(to right, rgb(99, 102, 241), rgb(79, 70, 229));
  }

  .status-transcribing {
    background: linear-gradient(to right, rgb(249, 115, 22), rgb(234, 88, 12));
  }

  .status-analyzing {
    background: linear-gradient(to right, rgb(217, 70, 239), rgb(192, 38, 211));
  }

  .status-completed {
    background: linear-gradient(to right, rgb(34, 197, 94), rgb(22, 163, 74));
  }

  .status-failed {
    background: linear-gradient(to right, rgb(239, 68, 68), rgb(220, 38, 38));
  }

  /* Upload Zone */
  .upload-zone {
    @apply relative border-2 border-dashed rounded-3xl p-12 text-center transition-all duration-300 cursor-pointer group;
    border-color: rgba(212, 212, 212, 0.5);
  }

  .upload-zone:hover {
    border-color: rgba(129, 140, 248, 0.7);
    background-color: rgba(238, 242, 255, 0.3);
  }

  .upload-zone.active {
    border-color: rgb(99, 102, 241);
    background-color: rgba(238, 242, 255, 0.5);
    transform: scale(1.05);
  }

  /* Tabs */
  .tab-button {
    @apply relative px-6 py-3 font-medium text-neutral-600 dark:text-neutral-400 transition-all duration-300 hover:text-neutral-900 dark:hover:text-neutral-100;
  }

  .tab-button.active {
    @apply text-primary-600 dark:text-primary-400;
  }

  .tab-button.active::after {
    content: '';
    @apply absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full;
  }

  /* Animations */
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }

  @keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  /* Glassmorphism utilities */
  .glass {
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background-color: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}
