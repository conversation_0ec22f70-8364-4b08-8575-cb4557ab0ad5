@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 15, 23, 42;
  --background-rgb: 255, 255, 255;
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 248, 250, 252;
    --background-rgb: 15, 23, 42;
    --glass-bg: rgba(15, 23, 42, 0.25);
    --glass-border: rgba(255, 255, 255, 0.1);
  }
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  overflow-x: hidden;
}

/* Beautiful background patterns */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

@layer base {
  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent leading-tight;
  }
  h2 {
    @apply text-2xl md:text-3xl font-semibold mb-4 text-neutral-800 dark:text-neutral-200;
  }
  h3 {
    @apply text-xl md:text-2xl font-medium mb-3 text-neutral-700 dark:text-neutral-300;
  }
  h4 {
    @apply text-lg md:text-xl font-medium mb-2 text-neutral-700 dark:text-neutral-300;
  }
  a {
    @apply text-primary-600 hover:text-primary-700 transition-all duration-300 hover:underline decoration-2 underline-offset-4;
  }
  p {
    @apply mb-4 text-neutral-600 dark:text-neutral-400 leading-relaxed;
  }
}

@layer components {
  /* Button Styles */
  .btn {
    @apply relative inline-flex items-center justify-center px-6 py-3 rounded-2xl font-medium transition-all duration-300 transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg shadow-primary-500/25 hover:shadow-xl hover:shadow-primary-500/40 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600 text-white shadow-lg shadow-secondary-500/25 hover:shadow-xl hover:shadow-secondary-500/40 focus:ring-secondary-500;
  }

  .btn-accent {
    @apply bg-gradient-to-r from-accent-500 to-accent-600 text-white shadow-lg shadow-accent-500/25 hover:shadow-xl hover:shadow-accent-500/40 focus:ring-accent-500;
  }

  .btn-ghost {
    @apply bg-white/10 backdrop-blur-sm border border-white/20 text-neutral-700 dark:text-neutral-300 hover:bg-white/20 hover:border-white/30 focus:ring-white/50;
  }

  .btn-outline {
    @apply border-2 border-primary-500 text-primary-600 hover:bg-primary-500 hover:text-white focus:ring-primary-500;
  }

  /* Card Styles */
  .card {
    @apply relative bg-white/80 dark:bg-neutral-900/80 backdrop-blur-xl border border-white/20 dark:border-neutral-800/50 rounded-3xl shadow-soft hover:shadow-medium transition-all duration-500 p-8;
  }

  .card-glass {
    @apply relative bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl shadow-large p-8;
  }

  .card-gradient {
    @apply relative bg-gradient-to-br from-white/90 to-white/70 dark:from-neutral-900/90 dark:to-neutral-900/70 backdrop-blur-xl border border-white/30 dark:border-neutral-700/50 rounded-3xl shadow-large p-8;
  }

  /* Input Styles */
  .input {
    @apply w-full px-4 py-3 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-700/50 rounded-2xl text-neutral-900 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-primary-500/20 focus:border-primary-500/50 hover:border-neutral-300/70 dark:hover:border-neutral-600/70;
  }

  .input-error {
    @apply border-red-500/50 focus:ring-red-500/20 focus:border-red-500/50;
  }

  /* Progress Bar */
  .progress-bar {
    @apply w-full h-3 bg-neutral-200/50 dark:bg-neutral-700/50 rounded-full overflow-hidden backdrop-blur-sm;
  }

  .progress-fill {
    @apply h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full transition-all duration-1000 ease-out relative;
  }

  .progress-fill::after {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse;
  }

  /* Status Indicators */
  .status-pending {
    @apply bg-gradient-to-r from-neutral-400 to-neutral-500;
  }

  .status-processing {
    @apply bg-gradient-to-r from-primary-500 to-primary-600;
  }

  .status-transcribing {
    @apply bg-gradient-to-r from-accent-500 to-accent-600;
  }

  .status-analyzing {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600;
  }

  .status-completed {
    @apply bg-gradient-to-r from-green-500 to-green-600;
  }

  .status-failed {
    @apply bg-gradient-to-r from-red-500 to-red-600;
  }

  /* Upload Zone */
  .upload-zone {
    @apply relative border-2 border-dashed border-neutral-300/50 dark:border-neutral-600/50 rounded-3xl p-12 text-center transition-all duration-300 cursor-pointer hover:border-primary-400/70 hover:bg-primary-50/30 dark:hover:bg-primary-900/20 group;
  }

  .upload-zone.active {
    @apply border-primary-500 bg-primary-50/50 dark:bg-primary-900/30 scale-105;
  }

  /* Tabs */
  .tab-button {
    @apply relative px-6 py-3 font-medium text-neutral-600 dark:text-neutral-400 transition-all duration-300 hover:text-neutral-900 dark:hover:text-neutral-100;
  }

  .tab-button.active {
    @apply text-primary-600 dark:text-primary-400;
  }

  .tab-button.active::after {
    content: '';
    @apply absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full;
  }

  /* Animations */
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }

  @keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  /* Glassmorphism utilities */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-white/10;
  }
}
