{"name": "new-voice-analysis", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.509.0", "next": "^15.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0"}, "devDependencies": {"@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.4", "@types/node": "^22.15.17", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "react-dropzone": "^14.3.8", "sonner": "^2.0.3", "tailwindcss": "^3.4.0", "typescript": "^5.8.3", "zustand": "^5.0.4"}}